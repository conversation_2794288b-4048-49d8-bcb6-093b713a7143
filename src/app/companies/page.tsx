import { Metadata } from 'next'
import { Suspense } from 'react'
import { Header } from '@/components/header'
import { CompanySearch } from '@/components/company-search'

function CompanySearchFallback() {
  return (
    <div className="flex justify-center items-center py-12">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>
  )
}

export const metadata: Metadata = {
  title: "Companies Directory",
  description: "Browse and search companies by their employee benefits, location, industry, and size. Find companies offering the benefits you care about most.",
  keywords: [
    "companies directory",
    "company search",
    "companies by benefits",
    "companies by location",
    "companies by industry",
    "employer search",
    "company benefits search",
    "German companies",
    "startup companies",
    "enterprise companies"
  ],
  openGraph: {
    title: "Companies Directory | BenefitLens",
    description: "Browse and search companies by their employee benefits, location, industry, and size. Find companies offering the benefits you care about most.",
    type: 'website',
  },
  alternates: {
    canonical: '/companies',
  },
}

export default function CompaniesPage() {
  return (
    <main className="container mx-auto px-4 py-6 sm:py-8">
      <div className="text-center mb-8 sm:mb-12">
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
          Companies Directory
        </h1>
        <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto px-2">
          Browse and search companies by their employee benefits, location, industry, and size. Find companies offering the benefits you care about most.
        </p>
      </div>

      <Suspense fallback={<CompanySearchFallback />}>
        <CompanySearch />
      </Suspense>
    </main>
  )
}
