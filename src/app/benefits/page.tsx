import { Metadata } from 'next'
import { Head<PERSON> } from '@/components/header'
import { BenefitsList } from '@/components/benefits-list'

export const metadata: Metadata = {
  title: "Employee Benefits Directory",
  description: "Explore the comprehensive list of employee benefits offered by companies. Find benefits like Wellpass, sabbatical leave, remote work, flexible hours, and more.",
  keywords: [
    "employee benefits list",
    "company benefits directory",
    "Wellpass",
    "sabbatical leave",
    "remote work benefits",
    "flexible working hours",
    "health benefits",
    "wellness benefits",
    "financial benefits",
    "development benefits"
  ],
  openGraph: {
    title: "Employee Benefits Directory | BenefitLens",
    description: "Explore the comprehensive list of employee benefits offered by companies. Find benefits like Wellpass, sabbatical leave, remote work, and more.",
    type: 'website',
  },
  alternates: {
    canonical: '/benefits',
  },
}

export default function BenefitsPage() {
  return (
    <main className="container mx-auto px-4 py-6 sm:py-8">
      <div className="text-center mb-8 sm:mb-12">
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
          Employee Benefits
        </h1>
        <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto px-2">
          Explore the comprehensive list of benefits offered by companies on our platform.
        </p>
      </div>



      <BenefitsList />
    </main>
  )
}
