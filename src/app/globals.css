@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #2563eb;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --muted: #f1f5f9;
  --muted-foreground: #475569;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #2563eb;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: #f9fafb; /* Force light gray background */
  color: var(--foreground);
  font-family: var(--font-sans);
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Mobile scrolling improvements */
.webkit-overflow-scrolling-touch {
  -webkit-overflow-scrolling: touch;
}

/* Ensure smooth scrolling on mobile */
@media (max-width: 640px) {
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* Improve touch scrolling for dropdowns */
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Ensure minimum touch target sizes for mobile accessibility */
  button,
  input[type="submit"],
  input[type="button"],
  a[role="button"],
  .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* Exception for small icon buttons that are part of larger touch targets */
  .icon-only-small {
    min-height: auto;
    min-width: auto;
  }
}

/* Safari-specific fixes */
@supports (-webkit-appearance: none) {
  /* Fix Safari dropdown positioning */
  .absolute {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  /* Improve Safari touch handling */
  button,
  [role="button"],
  .cursor-pointer {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Fix Safari input zoom */
  input[type="text"],
  input[type="email"],
  input[type="search"],
  textarea {
    font-size: 16px; /* Prevents zoom on iOS Safari */
    -webkit-appearance: none;
    appearance: none;
  }

  /* Safari scrolling improvements */
  .overflow-y-auto,
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

/* Cross-browser compatibility fixes */
* {
  /* Prevent text selection on interactive elements */
  -webkit-tap-highlight-color: transparent;
}

/* Fix for Safari flexbox issues */
.flex {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

/* Ensure proper z-index stacking */
.z-50 {
  z-index: 50;
}

.z-\[9999\] {
  z-index: 9999;
}
