import { NextRequest, NextResponse } from 'next/server'
import { trackCompanyView, trackSearch, trackBenefitInteraction } from '@/lib/analytics-tracker'
import { withErrorHandling } from '@/lib/api-error-handler'

// POST /api/analytics/track - Track analytics events
export const POST = withErrorHandling(async (request: NextRequest) => {
    const body = await request.json()
    // Support both formats: {type, data} and {eventType, eventData}
    const type = body.type || body.eventType
    const data = body.data || body.eventData

    if (!type || !data) {
      return NextResponse.json(
        { error: 'Missing type/eventType or data/eventData in request body' },
        { status: 400 }
      )
    }

    switch (type) {
      case 'company_view':
        if (!data.companyId) {
          return NextResponse.json(
            { error: 'Missing companyId for company_view event' },
            { status: 400 }
          )
        }
        await trackCompanyView(data.companyId, data.referrer)
        break

      case 'search':
        if (!data.queryText || data.resultsCount === undefined) {
          return NextResponse.json(
            { error: 'Missing queryText or resultsCount for search event' },
            { status: 400 }
          )
        }
        const searchId = await trackSearch(data.queryText, data.resultsCount, data.filtersApplied)
        return NextResponse.json({ success: true, searchId })

      case 'benefit_interaction':
        if (!data.benefitId || !data.companyId || !data.interactionType) {
          return NextResponse.json(
            { error: 'Missing benefitId, companyId, or interactionType for benefit_interaction event' },
            { status: 400 }
          )
        }
        await trackBenefitInteraction(
          data.benefitId,
          data.companyId,
          data.interactionType,
          data.searchQueryId
        )
        break

      default:
        // Handle generic events (for testing and future extensibility)
        console.log(`Generic analytics event tracked: ${type}`, data)
        break
    }

    return NextResponse.json({ success: true })
})
