/**
 * Responsive Design Validation Tests
 * 
 * These tests ensure consistent layout, button sizes, and text visibility
 * across all screen sizes and browsers, specifically targeting issues
 * mentioned by the user about Safari and mobile compatibility.
 */

import { test, expect, Page } from '@playwright/test'
import { signInUser, waitForPageLoad } from './auth-helpers'

test.describe('Responsive Design Validation', () => {
  
  const viewports = [
    { width: 375, height: 667, name: 'iPhone SE', type: 'mobile' },
    { width: 390, height: 844, name: 'iPhone 12', type: 'mobile' },
    { width: 414, height: 896, name: 'iPhone 11 Pro Max', type: 'mobile' },
    { width: 768, height: 1024, name: 'iPad Portrait', type: 'tablet' },
    { width: 1024, height: 768, name: 'iPad Landscape', type: 'tablet' },
    { width: 1440, height: 900, name: 'Desktop', type: 'desktop' },
    { width: 1920, height: 1080, name: 'Large Desktop', type: 'desktop' }
  ]

  for (const viewport of viewports) {
    test(`Layout Consistency - ${viewport.name}`, async ({ page }) => {
      await page.setViewportSize(viewport)
      await page.goto('/')
      await waitForPageLoad(page)

      console.log(`Testing ${viewport.name} (${viewport.width}x${viewport.height})`)

      // Test header layout
      const header = page.locator('header')
      await expect(header).toBeVisible()
      
      const headerBox = await header.boundingBox()
      if (headerBox) {
        expect(headerBox.width).toBeLessThanOrEqual(viewport.width)
        expect(headerBox.height).toBeGreaterThan(0)
      }

      // Test main content area
      const main = page.locator('main')
      await expect(main).toBeVisible()
      
      const mainBox = await main.boundingBox()
      if (mainBox) {
        expect(mainBox.width).toBeLessThanOrEqual(viewport.width)
      }

      // Test search form layout
      const searchForm = page.locator('form').first()
      await expect(searchForm).toBeVisible()
      
      const formBox = await searchForm.boundingBox()
      if (formBox) {
        expect(formBox.width).toBeLessThanOrEqual(viewport.width)
      }

      // Test filter grid layout
      const filterGrid = page.locator('div[class*="grid"]').filter({ hasText: /Select/ }).first()
      if (await filterGrid.isVisible()) {
        const gridBox = await filterGrid.boundingBox()
        if (gridBox) {
          expect(gridBox.width).toBeLessThanOrEqual(viewport.width)
        }
      }

      console.log(`✅ ${viewport.name} layout is consistent`)
    })

    test(`Button Touch Targets - ${viewport.name}`, async ({ page }) => {
      await page.setViewportSize(viewport)
      await page.goto('/')
      await waitForPageLoad(page)

      // Test button sizes based on device type
      const buttons = page.locator('button:visible')
      const buttonCount = await buttons.count()
      
      let validButtons = 0
      const minTouchSize = viewport.type === 'mobile' ? 44 : 40

      for (let i = 0; i < Math.min(buttonCount, 10); i++) {
        const button = buttons.nth(i)
        if (await button.isVisible()) {
          const box = await button.boundingBox()
          if (box) {
            if (box.height >= minTouchSize && box.width >= minTouchSize) {
              validButtons++
            } else {
              console.log(`⚠️ Button ${i} too small: ${box.width}x${box.height} (min: ${minTouchSize}x${minTouchSize})`)
            }
          }
        }
      }

      // At least 80% of buttons should meet touch target requirements
      const validPercentage = (validButtons / Math.min(buttonCount, 10)) * 100
      expect(validPercentage).toBeGreaterThanOrEqual(80)
      
      console.log(`✅ ${validButtons}/${Math.min(buttonCount, 10)} buttons meet touch target requirements on ${viewport.name}`)
    })

    test(`Filter Dropdown Functionality - ${viewport.name}`, async ({ page }) => {
      await page.setViewportSize(viewport)
      await page.goto('/')
      await waitForPageLoad(page)

      // Test each filter dropdown
      const filters = [
        { name: 'Company Size', selector: 'svg[class*="lucide-users"]' },
        { name: 'Industry', selector: 'svg[class*="lucide-building2"]' },
        { name: 'Benefits', selector: 'svg[class*="lucide-filter"]' }
      ]

      for (const filter of filters) {
        try {
          const filterIcon = page.locator(filter.selector).first()
          const filterContainer = filterIcon.locator('..').locator('..')
          
          if (await filterContainer.isVisible()) {
            // Click to open dropdown
            await filterContainer.click()
            await page.waitForTimeout(500)
            
            // Check if dropdown opened and is properly positioned
            const dropdown = page.locator('div[class*="absolute"][class*="z-"]').filter({ hasText: /Search/ }).first()
            
            if (await dropdown.isVisible()) {
              const dropdownBox = await dropdown.boundingBox()
              if (dropdownBox) {
                // Dropdown should not exceed viewport bounds
                expect(dropdownBox.x + dropdownBox.width).toBeLessThanOrEqual(viewport.width + 10) // Small tolerance
                expect(dropdownBox.y + dropdownBox.height).toBeLessThanOrEqual(viewport.height + 10)
                
                // Test search input within dropdown
                const searchInput = dropdown.locator('input[placeholder="Search..."]')
                if (await searchInput.isVisible()) {
                  await searchInput.fill('test')
                  await page.waitForTimeout(300)
                  await searchInput.clear()
                }
                
                console.log(`✅ ${filter.name} dropdown working on ${viewport.name}`)
              }
              
              // Close dropdown
              await page.click('body')
              await page.waitForTimeout(300)
            }
          }
        } catch (error) {
          console.log(`⚠️ ${filter.name} filter test failed on ${viewport.name}: ${error}`)
        }
      }
    })
  }

  test('Text Visibility and Readability', async ({ page }) => {
    const testViewports = [
      { width: 375, height: 667, name: 'iPhone SE' },
      { width: 768, height: 1024, name: 'iPad' },
      { width: 1440, height: 900, name: 'Desktop' }
    ]

    for (const viewport of testViewports) {
      await page.setViewportSize(viewport)
      await page.goto('/')
      await waitForPageLoad(page)

      // Test that text elements are visible and have proper contrast
      const textElements = [
        { selector: 'h1, h2, h3', name: 'Headings' },
        { selector: 'p', name: 'Paragraphs' },
        { selector: 'button', name: 'Button text' },
        { selector: 'input[placeholder]', name: 'Input placeholders' },
        { selector: 'label', name: 'Labels' }
      ]

      for (const element of textElements) {
        const elements = page.locator(element.selector)
        const count = await elements.count()
        
        if (count > 0) {
          const firstElement = elements.first()
          if (await firstElement.isVisible()) {
            // Check that text is not cut off
            const box = await firstElement.boundingBox()
            if (box) {
              expect(box.width).toBeGreaterThan(0)
              expect(box.height).toBeGreaterThan(0)
            }
          }
        }
      }

      console.log(`✅ Text visibility validated on ${viewport.name}`)
    }
  })

  test('Modal Responsive Behavior', async ({ page }) => {
    await signInUser(page, 'user1@techcorp.e2e')
    
    const testViewports = [
      { width: 375, height: 667, name: 'iPhone SE' },
      { width: 768, height: 1024, name: 'iPad' },
      { width: 1440, height: 900, name: 'Desktop' }
    ]

    for (const viewport of testViewports) {
      await page.setViewportSize(viewport)
      await page.goto('/dashboard')
      await waitForPageLoad(page)

      // Try to open a modal
      const addBenefitsButton = page.locator('button').filter({ hasText: /Add Benefits/ }).first()
      if (await addBenefitsButton.isVisible()) {
        await addBenefitsButton.click()
        await page.waitForTimeout(1000)

        const modal = page.locator('[role="dialog"]')
        if (await modal.isVisible()) {
          const modalBox = await modal.boundingBox()
          if (modalBox) {
            // Modal should fit within viewport with some padding
            expect(modalBox.width).toBeLessThanOrEqual(viewport.width)
            expect(modalBox.height).toBeLessThanOrEqual(viewport.height)
            
            // Modal should not be positioned off-screen
            expect(modalBox.x).toBeGreaterThanOrEqual(0)
            expect(modalBox.y).toBeGreaterThanOrEqual(0)
            
            console.log(`✅ Modal responsive on ${viewport.name}`)
          }

          // Close modal
          const closeButton = modal.locator('button').filter({ hasText: /close|cancel|×/i }).first()
          if (await closeButton.isVisible()) {
            await closeButton.click()
          } else {
            await page.keyboard.press('Escape')
          }
          await page.waitForTimeout(500)
        }
      }
    }
  })

  test('Content Overflow Prevention', async ({ page }) => {
    const testViewports = [
      { width: 320, height: 568, name: 'iPhone 5' }, // Very small screen
      { width: 375, height: 667, name: 'iPhone SE' },
      { width: 768, height: 1024, name: 'iPad' }
    ]

    for (const viewport of testViewports) {
      await page.setViewportSize(viewport)
      await page.goto('/')
      await waitForPageLoad(page)

      // Check for horizontal overflow
      const bodyWidth = await page.evaluate(() => document.body.scrollWidth)
      expect(bodyWidth).toBeLessThanOrEqual(viewport.width + 20) // Small tolerance for scrollbars

      // Check that no elements extend beyond viewport
      const allElements = await page.locator('*').all()
      let overflowCount = 0
      
      for (let i = 0; i < Math.min(allElements.length, 50); i++) {
        const element = allElements[i]
        if (await element.isVisible()) {
          const box = await element.boundingBox()
          if (box && box.x + box.width > viewport.width + 20) {
            overflowCount++
          }
        }
      }

      // Allow some overflow for dropdowns and modals
      expect(overflowCount).toBeLessThan(5)
      
      console.log(`✅ No significant content overflow on ${viewport.name}`)
    }
  })
})
