'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Search, Building2, Users, Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { CompanyCard } from '@/components/company-card'
import { SearchableMultiSelect } from '@/components/ui/searchable-multi-select'
import { LocationInput } from '@/components/ui/location-input'
// Removed benefit-utils import - using simple comma-separated approach for both filters
import type { Company, CompanyBenefit } from '@/types/database'

interface CompanyWithBenefits extends Company {
  company_benefits?: CompanyBenefit[]
}

export function CompanySearch() {
  const [companies, setCompanies] = useState<CompanyWithBenefits[]>([])
  const [totalCompanies, setTotalCompanies] = useState(0)
  const [loading, setLoading] = useState(false)
  const [loadingMore, setLoadingMore] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [lastLoadedPage, setLastLoadedPage] = useState(0)
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState({
    location: '',
    size: '',
    industry: '',
    benefits: '',
  })
  const [activeBenefitFilters, setActiveBenefitFilters] = useState<string[]>([])
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([])
  const [selectedSizes, setSelectedSizes] = useState<string[]>([])
  const [benefitOptions, setBenefitOptions] = useState<Array<{value: string, label: string, count: number}>>([])
  const [industryOptions, setIndustryOptions] = useState<Array<{value: string, label: string, count: number}>>([])
  const [sizeOptions, setSizeOptions] = useState<Array<{value: string, label: string, count: number}>>([])
  const [loadingOptions, setLoadingOptions] = useState(false)
  const [urlParsed, setUrlParsed] = useState(false)

  const searchParams = useSearchParams()
  const router = useRouter()

  const fetchCompanies = useCallback(async (page = 1, append = false) => {
    if (append) {
      setLoadingMore(true)
    } else {
      setLoading(true)
      setCurrentPage(1)
      setHasMore(true)
      setLastLoadedPage(0)
    }

    try {
      const params = new URLSearchParams()
      params.append('page', page.toString())
      params.append('limit', '30')

      if (searchQuery) {params.append('search', searchQuery)}
      if (filters.location) {params.append('location', filters.location)}

      // Use selected sizes from multi-select (consistent with industry/benefits approach)
      if (selectedSizes.length > 0) {
        params.append('size', selectedSizes.join(','))
      }

      // Use selected industries from multi-select
      if (selectedIndustries.length > 0) {
        params.append('industry', selectedIndustries.join(','))
      }

      // Use selected benefits from multi-select (consistent with industry approach)
      if (activeBenefitFilters.length > 0) {
        params.append('benefits', activeBenefitFilters.join(','))
      }

      const response = await fetch(`/api/companies?${params}`)
      if (response.ok) {
        const data = await response.json()
        const newCompanies = data.companies || []

        if (append) {
          setCompanies(prev => {
            // Filter out any companies that already exist to prevent duplicates
            const existingIds = new Set(prev.map((company: CompanyWithBenefits) => company.id))
            const uniqueNewCompanies = newCompanies.filter((company: CompanyWithBenefits) => !existingIds.has(company.id))
            return [...prev, ...uniqueNewCompanies]
          })
          setCurrentPage(page)
          setLastLoadedPage(page)
        } else {
          setCompanies(newCompanies)
          setLastLoadedPage(1)
        }

        setTotalCompanies(data.total || 0)
        setHasMore(newCompanies.length === 30 && (append ? companies.length + newCompanies.length : newCompanies.length) < (data.total || 0))

        // Track search if there's a search query (only for initial search, not pagination)
        if (!append && searchQuery && searchQuery.trim().length >= 2) {
          try {
            await fetch('/api/analytics/track', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                type: 'search',
                data: {
                  queryText: searchQuery.trim(),
                  resultsCount: data.total || 0,
                  filtersApplied: {
                    location: filters.location,
                    sizes: selectedSizes,
                    industries: selectedIndustries,
                    benefits: activeBenefitFilters
                  }
                }
              })
            })
          } catch (trackingError) {
            // Don't break the search if tracking fails
            console.warn('Failed to track search:', trackingError)
          }
        }
      }
    } catch (error) {
      console.error('Error fetching companies:', error)
    } finally {
      setLoading(false)
      setLoadingMore(false)
    }
  }, [searchQuery, filters.location, selectedSizes, activeBenefitFilters, selectedIndustries, companies.length])

  // Fetch filter options
  const fetchFilterOptions = async () => {
    setLoadingOptions(true)
    try {
      const [benefitsResponse, industriesResponse, sizesResponse] = await Promise.all([
        fetch('/api/filter-options/benefits'),
        fetch('/api/filter-options/industries'),
        fetch('/api/filter-options/sizes')
      ])

      if (benefitsResponse.ok) {
        const benefits = await benefitsResponse.json()
        setBenefitOptions(benefits)
      }

      if (industriesResponse.ok) {
        const industries = await industriesResponse.json()
        setIndustryOptions(industries)
      }

      if (sizesResponse.ok) {
        const sizes = await sizesResponse.json()
        setSizeOptions(sizes)
      }
    } catch (error) {
      console.error('Error fetching filter options:', error)
    } finally {
      setLoadingOptions(false)
    }
  }

  // Initialize filters from URL parameters
  useEffect(() => {
    const benefitsParam = searchParams.get('benefits')
    if (benefitsParam) {
      setActiveBenefitFilters(benefitsParam.split(',').filter(Boolean))
    } else {
      setActiveBenefitFilters([])
    }

    const industryParam = searchParams.get('industry')
    if (industryParam) {
      setSelectedIndustries(industryParam.split(',').filter(Boolean))
    } else {
      setSelectedIndustries([])
    }

    const sizeParam = searchParams.get('size')
    if (sizeParam) {
      setSelectedSizes(sizeParam.split(',').filter(Boolean))
    } else {
      setSelectedSizes([])
    }

    const searchParam = searchParams.get('search')
    if (searchParam) {
      setSearchQuery(searchParam)
    } else {
      setSearchQuery('')
    }

    fetchFilterOptions()
    setUrlParsed(true)
  }, [searchParams])

  useEffect(() => {
    if (urlParsed) {
      fetchCompanies(1, false)
    }
  }, [urlParsed, searchQuery, filters.location, selectedSizes, activeBenefitFilters, selectedIndustries])

  // Infinite scroll logic
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null

    const handleScroll = () => {
      if (loading || loadingMore || !hasMore) {return}

      // Throttle scroll events to prevent rapid firing
      if (timeoutId) {return}

      timeoutId = setTimeout(() => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop
        const windowHeight = window.innerHeight
        const documentHeight = document.documentElement.scrollHeight

        // Load more when user is 200px from bottom
        if (scrollTop + windowHeight >= documentHeight - 200) {
          const nextPage = currentPage + 1
          // Prevent loading the same page multiple times
          if (nextPage > lastLoadedPage) {
            fetchCompanies(nextPage, true)
          }
        }

        timeoutId = null
      }, 100) // 100ms throttle
    }

    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
      if (timeoutId) {clearTimeout(timeoutId)}
    }
  }, [loading, loadingMore, hasMore, currentPage, lastLoadedPage, fetchCompanies])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchCompanies(1, false)
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  // Removed removeBenefitFilter and clearAllBenefitFilters functions
  // Benefits are now managed only through the SearchableMultiSelect component
  // to maintain consistency with industry filter behavior

  const handleBenefitSelectionChange = (selectedBenefits: string[]) => {
    setActiveBenefitFilters(selectedBenefits)

    // Update URL
    const params = new URLSearchParams(searchParams.toString())
    if (selectedBenefits.length > 0) {
      params.set('benefits', selectedBenefits.join(','))
    } else {
      params.delete('benefits')
    }

    const newUrl = params.toString() ? `/?${params.toString()}` : '/'
    router.replace(newUrl)
  }

  const handleIndustrySelectionChange = (selectedIndustryList: string[]) => {
    setSelectedIndustries(selectedIndustryList)

    // Update URL
    const params = new URLSearchParams(searchParams.toString())
    if (selectedIndustryList.length > 0) {
      params.set('industry', selectedIndustryList.join(','))
    } else {
      params.delete('industry')
    }

    const newUrl = params.toString() ? `/?${params.toString()}` : '/'
    router.replace(newUrl)
  }

  const handleSizeSelectionChange = (selectedSizeList: string[]) => {
    setSelectedSizes(selectedSizeList)

    // Update URL
    const params = new URLSearchParams(searchParams.toString())
    if (selectedSizeList.length > 0) {
      params.set('size', selectedSizeList.join(','))
    } else {
      params.delete('size')
    }

    const newUrl = params.toString() ? `/?${params.toString()}` : '/'
    router.replace(newUrl)
  }



  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Search Form */}
      <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-4 h-4 sm:w-5 sm:h-5" />
            <input
              type="text"
              placeholder="Search for benefits or companies..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-9 sm:pl-10 pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base"
            />
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            <LocationInput
              value={filters.location}
              onChange={(location) => handleFilterChange('location', location)}
              placeholder="Location"
              className="text-sm sm:text-base"
            />

            <SearchableMultiSelect
              options={sizeOptions}
              selectedValues={selectedSizes}
              onSelectionChange={handleSizeSelectionChange}
              placeholder="Select Company Sizes"
              icon={<Users className="w-4 h-4" />}
              loading={loadingOptions}
              maxDisplayed={2}
            />

            <SearchableMultiSelect
              options={industryOptions}
              selectedValues={selectedIndustries}
              onSelectionChange={handleIndustrySelectionChange}
              placeholder="Select Industries"
              icon={<Building2 className="w-4 h-4" />}
              loading={loadingOptions}
              maxDisplayed={2}
            />

            <SearchableMultiSelect
              options={benefitOptions}
              selectedValues={activeBenefitFilters}
              onSelectionChange={handleBenefitSelectionChange}
              placeholder="Select Benefits"
              icon={<Filter className="w-4 h-4" />}
              loading={loadingOptions}
              maxDisplayed={2}
            />
          </div>

          <Button type="submit" variant="search" className="w-full sm:w-auto" disabled={loading}>
            {loading ? 'Searching...' : 'Search'}
          </Button>
        </form>
      </div>

      {/* Removed Active Benefit Filters section to maintain consistency with industry filter behavior */}

      {/* Results */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2 text-sm sm:text-base">Loading companies...</p>
          </div>
        ) : companies.length > 0 ? (
          <>
            <p className="text-gray-600 text-sm sm:text-base px-1">
              Found {totalCompanies} companies{companies.length < totalCompanies ? ` (showing ${companies.length})` : ''}
            </p>
            <div className="grid grid-cols-1 gap-3 sm:gap-4">
              {companies.map((company) => (
                <CompanyCard key={company.id} company={company} />
              ))}
            </div>

            {/* Loading more indicator */}
            {loadingMore && (
              <div className="text-center py-6">
                <div className="animate-spin rounded-full h-5 w-5 sm:h-6 sm:w-6 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-600 mt-2 text-xs sm:text-sm">Loading more companies...</p>
              </div>
            )}

            {/* End of results indicator */}
            {!hasMore && !loadingMore && companies.length > 0 && (
              <div className="text-center py-6">
                <p className="text-gray-500 text-xs sm:text-sm">You've reached the end of the results</p>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-600 text-sm sm:text-base px-4">No companies found. Try adjusting your search criteria.</p>
          </div>
        )}
      </div>
    </div>
  )
}
