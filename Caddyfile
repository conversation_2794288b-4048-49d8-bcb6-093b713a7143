# Caddyfile for benefitlens.de
benefitlens.de, www.benefitlens.de {
  # Use CF Origin cert & key you generated in Cloudflare → SSL/TLS → Origin
  tls /etc/caddy/benefitlens.pem /etc/caddy/benefitlens.key

  # Forward traffic to your app running locally
  reverse_proxy benefitlens-app:3000 {
    # Forward Cloudflare's real client IP
    header_up X-Real-IP {http.request.header.CF-Connecting-IP}
    header_up X-Forwarded-For {http.request.header.CF-Connecting-IP}
  }

  # Access log -> /var/log/caddy/benefitlens-access.log (inside container)
  log {
    output file /var/log/caddy/benefitlens-access.log {
        roll_size 50MiB       # smaller chunks
        roll_keep 7           # last 7 files
        roll_keep_for 168h    # 7 days
        # roll_uncompressed   # uncomment if you don't want gzip (not recommended)
        roll_local_time     # filenames use local time instead of UTC
      }
    format json
    level info
  }
}