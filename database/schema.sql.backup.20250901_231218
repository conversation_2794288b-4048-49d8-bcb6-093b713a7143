--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13 (Debian 15.13-1.pgdg120+1)
-- Dumped by pg_dump version 15.13 (Debian 15.13-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: cleanup_expired_cache(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.cleanup_expired_cache() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_store WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Also cleanup expired CSRF tokens
    DELETE FROM csrf_tokens WHERE expires_at < NOW();
    
    -- Cleanup expired rate limits
    DELETE FROM rate_limits WHERE expires_at < NOW();
    
    RETURN deleted_count;
END;
$$;


--
-- Name: cleanup_expired_magic_links(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.cleanup_expired_magic_links() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    DELETE FROM magic_link_tokens WHERE expires_at < NOW();
    DELETE FROM magic_link_rate_limits WHERE window_start < NOW() - INTERVAL '1 hour';
END;
$$;


--
-- Name: cleanup_expired_sessions(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.cleanup_expired_sessions() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup operation
    INSERT INTO activity_log (
        event_type,
        event_description,
        metadata,
        created_at
    ) VALUES (
        'session_cleanup',
        'Automated cleanup of expired sessions',
        jsonb_build_object('deleted_sessions', deleted_count),
        NOW()
    );
    
    RETURN deleted_count;
END;
$$;


--
-- Name: clear_cache_pattern(character varying); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.clear_cache_pattern(pattern character varying) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_store WHERE cache_key LIKE pattern;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;


--
-- Name: delete_all_user_sessions(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.delete_all_user_sessions(target_user_id uuid) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions 
    WHERE user_id = target_user_id;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the operation
    INSERT INTO activity_log (
        event_type,
        event_description,
        user_id,
        metadata,
        created_at
    ) VALUES (
        'user_sessions_deleted',
        'All sessions deleted for user',
        target_user_id::VARCHAR,
        jsonb_build_object('deleted_sessions', deleted_count),
        NOW()
    );
    
    RETURN deleted_count;
END;
$$;


--
-- Name: delete_cache(character varying); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.delete_cache(key character varying) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_store WHERE cache_key = key;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count > 0;
END;
$$;


--
-- Name: get_active_session_stats(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_active_session_stats() RETURNS TABLE(total_sessions integer, expired_sessions integer, active_sessions integer, oldest_session timestamp with time zone, newest_session timestamp with time zone)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_sessions,
        COUNT(*) FILTER (WHERE expires_at < NOW())::INTEGER as expired_sessions,
        COUNT(*) FILTER (WHERE expires_at >= NOW())::INTEGER as active_sessions,
        MIN(created_at) as oldest_session,
        MAX(created_at) as newest_session
    FROM user_sessions;
END;
$$;


--
-- Name: get_cache(character varying); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_cache(key character varying) RETURNS jsonb
    LANGUAGE plpgsql
    AS $$
DECLARE
    result JSONB;
BEGIN
    SELECT cache_value INTO result
    FROM cache_store
    WHERE cache_key = key AND expires_at > NOW();
    
    RETURN result;
END;
$$;


--
-- Name: FUNCTION get_cache(key character varying); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_cache(key character varying) IS 'Returns JSONB data that is automatically parsed by pg library - no additional JSON.parse() needed in JavaScript';


--
-- Name: get_csrf_token(character varying); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_csrf_token(session_id character varying) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
DECLARE
    result VARCHAR;
BEGIN
    SELECT token INTO result
    FROM csrf_tokens
    WHERE session_id = session_id AND expires_at > NOW();
    
    RETURN result;
END;
$$;


--
-- Name: get_session_config(character varying); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_session_config(config_name character varying) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
    config_value TEXT;
BEGIN
    SELECT setting_value INTO config_value
    FROM session_config
    WHERE setting_name = config_name;
    
    RETURN config_value;
END;
$$;


--
-- Name: refresh_cache_views(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.refresh_cache_views() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Refresh materialized views
    REFRESH MATERIALIZED VIEW CONCURRENTLY companies_with_benefits_cache;
    REFRESH MATERIALIZED VIEW CONCURRENTLY benefits_with_categories_cache;
    
    -- Log the refresh
    INSERT INTO activity_log (
        event_type,
        event_description,
        created_at
    ) VALUES (
        'cache_refresh',
        'Materialized views refreshed',
        NOW()
    );
END;
$$;


--
-- Name: set_cache(character varying, jsonb, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.set_cache(key character varying, value jsonb, ttl_seconds integer DEFAULT 3600) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO cache_store (cache_key, cache_value, expires_at)
    VALUES (key, value, NOW() + (ttl_seconds || ' seconds')::INTERVAL)
    ON CONFLICT (cache_key) 
    DO UPDATE SET 
        cache_value = EXCLUDED.cache_value,
        expires_at = EXCLUDED.expires_at,
        updated_at = NOW();
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$;


--
-- Name: set_csrf_token(character varying, character varying, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.set_csrf_token(session_id character varying, token character varying, ttl_seconds integer DEFAULT 3600) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO csrf_tokens (session_id, token, expires_at)
    VALUES (session_id, token, NOW() + (ttl_seconds || ' seconds')::INTERVAL)
    ON CONFLICT (session_id)
    DO UPDATE SET 
        token = EXCLUDED.token,
        expires_at = EXCLUDED.expires_at,
        created_at = NOW();
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$;


--
-- Name: update_company_analytics_summary(uuid, date); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_company_analytics_summary(target_company_id uuid, target_date date DEFAULT CURRENT_DATE) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO company_analytics_summary (
        company_id,
        date,
        page_views,
        unique_visitors,
        benefit_interactions,
        search_appearances
    )
    SELECT
        target_company_id,
        target_date,
        COALESCE(views.page_views, 0),
        COALESCE(views.unique_visitors, 0),
        COALESCE(interactions.benefit_interactions, 0),
        COALESCE(appearances.search_appearances, 0)
    FROM (
        SELECT
            COUNT(*) as page_views,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_visitors
        FROM company_page_views
        WHERE company_id = target_company_id
        AND DATE(created_at) = target_date
    ) views
    CROSS JOIN (
        SELECT
            COUNT(*) as benefit_interactions
        FROM benefit_search_interactions
        WHERE company_id = target_company_id
        AND DATE(created_at) = target_date
    ) interactions
    CROSS JOIN (
        SELECT
            COUNT(DISTINCT sq.id) as search_appearances
        FROM search_queries sq
        JOIN benefit_search_interactions bsi ON sq.id = bsi.search_query_id
        WHERE bsi.company_id = target_company_id
        AND DATE(sq.created_at) = target_date
    ) appearances
    ON CONFLICT (company_id, date) DO UPDATE SET
        page_views = EXCLUDED.page_views,
        unique_visitors = EXCLUDED.unique_visitors,
        benefit_interactions = EXCLUDED.benefit_interactions,
        search_appearances = EXCLUDED.search_appearances,
        updated_at = NOW();
END;
$$;


--
-- Name: update_daily_analytics_summary(date); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_daily_analytics_summary(target_date date DEFAULT CURRENT_DATE) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO daily_analytics_summary (
        date,
        total_company_views,
        total_searches,
        total_benefit_interactions,
        unique_visitors,
        unique_searchers,
        top_searched_benefits,
        top_viewed_companies
    )
    SELECT
        target_date,
        COALESCE(company_views.total_views, 0),
        COALESCE(searches.total_searches, 0),
        COALESCE(interactions.total_interactions, 0),
        COALESCE(company_views.unique_visitors, 0),
        COALESCE(searches.unique_searchers, 0),
        COALESCE(top_benefits.benefits, '[]'::jsonb),
        COALESCE(top_companies.companies, '[]'::jsonb)
    FROM (
        -- Company views for the day
        SELECT
            COUNT(*) as total_views,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_visitors
        FROM company_page_views
        WHERE DATE(created_at) = target_date
    ) company_views
    CROSS JOIN (
        -- Searches for the day
        SELECT
            COUNT(*) as total_searches,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_searchers
        FROM search_queries
        WHERE DATE(created_at) = target_date
    ) searches
    CROSS JOIN (
        -- Benefit interactions for the day
        SELECT
            COUNT(*) as total_interactions
        FROM benefit_search_interactions
        WHERE DATE(created_at) = target_date
    ) interactions
    CROSS JOIN (
        -- Top searched benefits
        SELECT
            COALESCE(jsonb_agg(
                jsonb_build_object(
                    'benefit_id', b.id,
                    'benefit_name', b.name,
                    'search_count', benefit_searches.search_count
                )
                ORDER BY benefit_searches.search_count DESC
            ) FILTER (WHERE benefit_searches.search_count > 0), '[]'::jsonb) as benefits
        FROM (
            SELECT
                bsi.benefit_id,
                COUNT(*) as search_count
            FROM benefit_search_interactions bsi
            JOIN search_queries sq ON bsi.search_query_id = sq.id
            WHERE DATE(sq.created_at) = target_date
            GROUP BY bsi.benefit_id
            ORDER BY search_count DESC
            LIMIT 10
        ) benefit_searches
        JOIN benefits b ON benefit_searches.benefit_id = b.id
    ) top_benefits
    CROSS JOIN (
        -- Top viewed companies
        SELECT
            COALESCE(jsonb_agg(
                jsonb_build_object(
                    'company_id', c.id,
                    'company_name', c.name,
                    'view_count', company_views.view_count
                )
                ORDER BY company_views.view_count DESC
            ) FILTER (WHERE company_views.view_count > 0), '[]'::jsonb) as companies
        FROM (
            SELECT
                cpv.company_id,
                COUNT(*) as view_count
            FROM company_page_views cpv
            WHERE DATE(cpv.created_at) = target_date
            GROUP BY cpv.company_id
            ORDER BY view_count DESC
            LIMIT 10
        ) company_views
        JOIN companies c ON company_views.company_id = c.id
    ) top_companies
    ON CONFLICT (date) DO UPDATE SET
        total_company_views = EXCLUDED.total_company_views,
        total_searches = EXCLUDED.total_searches,
        total_benefit_interactions = EXCLUDED.total_benefit_interactions,
        unique_visitors = EXCLUDED.unique_visitors,
        unique_searchers = EXCLUDED.unique_searchers,
        top_searched_benefits = EXCLUDED.top_searched_benefits,
        top_viewed_companies = EXCLUDED.top_viewed_companies,
        updated_at = NOW();
END;
$$;


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: activity_log; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.activity_log (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    event_type character varying(50) NOT NULL,
    event_description text NOT NULL,
    user_id character varying(255),
    user_email character varying(255),
    user_name character varying(255),
    company_id uuid,
    company_name character varying(255),
    benefit_id uuid,
    benefit_name character varying(255),
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT activity_log_event_type_check CHECK (((event_type)::text = ANY ((ARRAY['benefit_automatically_removed'::character varying, 'benefit_disputed'::character varying, 'benefit_removal_dispute_approved'::character varying, 'benefit_removal_dispute_cancelled'::character varying, 'benefit_removal_dispute_rejected'::character varying, 'benefit_removal_dispute_submitted'::character varying, 'benefit_verified'::character varying, 'cache_refresh'::character varying, 'company_added'::character varying, 'company_deleted'::character varying, 'session_cleanup'::character varying, 'user_deleted'::character varying, 'user_registered'::character varying])::text[])))
);


--
-- Name: TABLE activity_log; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.activity_log IS 'Tracks all system activities for admin dashboard and audit purposes';


--
-- Name: COLUMN activity_log.event_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.activity_log.event_type IS 'Type of event: benefit_automatically_removed, benefit_disputed, benefit_removal_dispute_approved, benefit_removal_dispute_cancelled, benefit_removal_dispute_rejected, benefit_removal_dispute_submitted, benefit_verified, cache_refresh, company_added, company_deleted, session_cleanup, user_deleted, user_registered';


--
-- Name: COLUMN activity_log.event_description; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.activity_log.event_description IS 'Human-readable description of the event for display in admin dashboard';


--
-- Name: COLUMN activity_log.metadata; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.activity_log.metadata IS 'Additional event-specific data stored as JSON';


--
-- Name: auth_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.auth_logs (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    event_type character varying(50) NOT NULL,
    status character varying(20) NOT NULL,
    email character varying(255),
    error_type character varying(100),
    error_message text,
    ip_address inet,
    user_agent text,
    token_used character varying(20),
    failure_reason text,
    additional_context jsonb,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT auth_logs_event_type_check CHECK (((event_type)::text = ANY (ARRAY[('sign_in_request'::character varying)::text, ('sign_up_request'::character varying)::text, ('magic_link_verification'::character varying)::text, ('session_creation'::character varying)::text, ('rate_limit_hit'::character varying)::text]))),
    CONSTRAINT auth_logs_status_check CHECK (((status)::text = ANY (ARRAY[('success'::character varying)::text, ('failure'::character varying)::text])))
);


--
-- Name: TABLE auth_logs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.auth_logs IS 'Detailed logs of authentication events for security monitoring and admin review';


--
-- Name: COLUMN auth_logs.event_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.auth_logs.event_type IS 'Type of authentication event (sign_in_request, sign_up_request, etc.)';


--
-- Name: COLUMN auth_logs.status; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.auth_logs.status IS 'Success or failure status of the authentication event';


--
-- Name: COLUMN auth_logs.error_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.auth_logs.error_type IS 'Categorized error type for failed events';


--
-- Name: COLUMN auth_logs.token_used; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.auth_logs.token_used IS 'Partial magic link token (first 8 chars) for tracking';


--
-- Name: COLUMN auth_logs.failure_reason; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.auth_logs.failure_reason IS 'Human-readable explanation of why the authentication failed';


--
-- Name: COLUMN auth_logs.additional_context; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.auth_logs.additional_context IS 'Additional context data in JSON format';


--
-- Name: benefit_categories; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.benefit_categories (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(100) NOT NULL,
    display_name character varying(255) NOT NULL,
    description text,
    icon character varying(50),
    sort_order integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE benefit_categories; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.benefit_categories IS 'Benefit categories table - active status is now determined dynamically based on benefit count';


--
-- Name: benefit_removal_disputes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.benefit_removal_disputes (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_benefit_id uuid NOT NULL,
    user_id uuid NOT NULL,
    reason text NOT NULL,
    status character varying(50) DEFAULT 'pending'::character varying,
    admin_user_id uuid,
    admin_comment text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT benefit_removal_disputes_status_check CHECK (((status)::text = ANY (ARRAY[('pending'::character varying)::text, ('approved'::character varying)::text, ('rejected'::character varying)::text, ('cancelled'::character varying)::text])))
);


--
-- Name: TABLE benefit_removal_disputes; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.benefit_removal_disputes IS 'User disputes against automatic benefit removals, requiring admin review';


--
-- Name: benefit_search_interactions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.benefit_search_interactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    search_query_id uuid,
    benefit_id uuid NOT NULL,
    company_id uuid NOT NULL,
    interaction_type character varying(50) NOT NULL,
    user_id uuid,
    session_id character varying(255),
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT benefit_search_interactions_interaction_type_check CHECK (((interaction_type)::text = ANY (ARRAY[('view'::character varying)::text, ('click'::character varying)::text, ('verify'::character varying)::text, ('dispute'::character varying)::text])))
);


--
-- Name: TABLE benefit_search_interactions; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.benefit_search_interactions IS 'User interactions with benefits in search results for engagement tracking';


--
-- Name: benefit_verifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.benefit_verifications (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_benefit_id uuid NOT NULL,
    user_id character varying(255) NOT NULL,
    status character varying(50) NOT NULL,
    comment text,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT benefit_verifications_status_check CHECK (((status)::text = ANY (ARRAY[('confirmed'::character varying)::text, ('disputed'::character varying)::text])))
);


--
-- Name: TABLE benefit_verifications; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.benefit_verifications IS 'User-submitted verifications confirming or disputing company benefits';


--
-- Name: benefits; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.benefits (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    icon character varying(255),
    created_at timestamp with time zone DEFAULT now(),
    description text,
    category_id uuid NOT NULL
);


--
-- Name: TABLE benefits; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.benefits IS 'Master list of all available employee benefits that companies can offer';


--
-- Name: companies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.companies (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    size character varying(50) NOT NULL,
    industry character varying(255) NOT NULL,
    description text,
    domain character varying(255),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    career_url character varying(500),
    website character varying(500),
    logo_url character varying(500),
    founded_year integer,
    CONSTRAINT companies_size_check CHECK (((size)::text = ANY (ARRAY[('startup'::character varying)::text, ('small'::character varying)::text, ('medium'::character varying)::text, ('large'::character varying)::text, ('enterprise'::character varying)::text])))
);


--
-- Name: TABLE companies; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.companies IS 'Companies table - user access controlled by email domain matching and explicit company_id in users table';


--
-- Name: COLUMN companies.website; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.companies.website IS 'Company website URL';


--
-- Name: COLUMN companies.logo_url; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.companies.logo_url IS 'URL to company logo image';


--
-- Name: COLUMN companies.founded_year; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.companies.founded_year IS 'Year the company was founded';


--
-- Name: company_benefits; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.company_benefits (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    benefit_id uuid NOT NULL,
    added_by character varying(255),
    is_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE company_benefits; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.company_benefits IS 'Junction table linking companies to the benefits they offer, with verification status tracking';


--
-- Name: benefits_with_categories_cache; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.benefits_with_categories_cache AS
 SELECT b.id,
    b.name,
    b.category_id,
    b.icon,
    b.description,
    b.created_at,
    bc.name AS category,
    bc.display_name AS category_display_name,
    bc.icon AS category_icon,
    bc.description AS category_description,
    count(cb.company_id) AS company_count
   FROM (((public.benefits b
     LEFT JOIN public.benefit_categories bc ON ((b.category_id = bc.id)))
     LEFT JOIN public.company_benefits cb ON ((b.id = cb.benefit_id)))
     LEFT JOIN public.companies c ON ((cb.company_id = c.id)))
  GROUP BY b.id, b.name, b.category_id, b.icon, b.description, b.created_at, bc.name, bc.display_name, bc.icon, bc.description
  ORDER BY b.name
  WITH NO DATA;


--
-- Name: cache_store; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cache_store (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    cache_key character varying(255) NOT NULL,
    cache_value jsonb NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE cache_store; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.cache_store IS 'Application-level caching system for improved performance';


--
-- Name: company_locations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.company_locations (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    location_raw character varying(255) NOT NULL,
    location_normalized character varying(255) NOT NULL,
    city character varying(100),
    country character varying(100),
    country_code character varying(2),
    latitude numeric(10,8),
    longitude numeric(11,8),
    is_primary boolean DEFAULT false,
    is_headquarters boolean DEFAULT false,
    location_type character varying(50) DEFAULT 'office'::character varying,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT company_locations_location_type_check CHECK (((location_type)::text = ANY (ARRAY[('office'::character varying)::text, ('headquarters'::character varying)::text, ('branch'::character varying)::text, ('remote'::character varying)::text])))
);


--
-- Name: TABLE company_locations; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.company_locations IS 'Geographic locations where companies operate, with normalized city data';


--
-- Name: companies_with_benefits_cache; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.companies_with_benefits_cache AS
 SELECT c.id,
    c.name,
    c.description,
    c.website,
    c.logo_url,
    c.size,
    c.industry,
    c.founded_year,
    c.created_at,
    c.updated_at,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', cb.id, 'benefit_id', cb.benefit_id, 'is_verified', cb.is_verified, 'added_by', cb.added_by, 'created_at', cb.created_at, 'benefit', jsonb_build_object('id', b.id, 'name', b.name, 'category_id', b.category_id, 'icon', b.icon, 'category', jsonb_build_object('id', bc.id, 'name', bc.name, 'display_name', bc.display_name, 'icon', bc.icon, 'description', bc.description)))) FILTER (WHERE (cb.id IS NOT NULL)), '[]'::json) AS company_benefits,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', cl.id, 'location_type', cl.location_type, 'city', cl.city, 'country', cl.country, 'country_code', cl.country_code, 'is_primary', cl.is_primary, 'is_headquarters', cl.is_headquarters, 'location_raw', cl.location_raw, 'location_normalized', cl.location_normalized)) FILTER (WHERE (cl.id IS NOT NULL)), '[]'::json) AS locations
   FROM ((((public.companies c
     LEFT JOIN public.company_benefits cb ON ((c.id = cb.company_id)))
     LEFT JOIN public.benefits b ON ((cb.benefit_id = b.id)))
     LEFT JOIN public.benefit_categories bc ON ((b.category_id = bc.id)))
     LEFT JOIN public.company_locations cl ON ((c.id = cl.company_id)))
  GROUP BY c.id, c.name, c.description, c.website, c.logo_url, c.size, c.industry, c.founded_year, c.created_at, c.updated_at
  WITH NO DATA;


--
-- Name: company_analytics_summary; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.company_analytics_summary (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    date date NOT NULL,
    page_views integer DEFAULT 0,
    unique_visitors integer DEFAULT 0,
    benefit_interactions integer DEFAULT 0,
    search_appearances integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE company_analytics_summary; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.company_analytics_summary IS 'Daily aggregated analytics data per company for performance tracking';


--
-- Name: company_page_views; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.company_page_views (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    user_id uuid,
    session_id character varying(255),
    ip_address inet,
    user_agent text,
    referrer text,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE company_page_views; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.company_page_views IS 'Individual page view events for company profile analytics';


--
-- Name: company_verification_tokens; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.company_verification_tokens (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    token character varying(255) NOT NULL,
    user_id uuid NOT NULL,
    user_email character varying(255) NOT NULL,
    company_id uuid NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    used_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE company_verification_tokens; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.company_verification_tokens IS 'Tokens for company email domain verification process';


--
-- Name: content_moderation_queue; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.content_moderation_queue (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    content_type character varying(50) NOT NULL,
    content_id character varying(255) NOT NULL,
    content_text text NOT NULL,
    user_id uuid,
    moderation_result jsonb,
    status character varying(50) DEFAULT 'pending'::character varying NOT NULL,
    priority character varying(20) DEFAULT 'normal'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    reviewed_at timestamp with time zone,
    reviewed_by uuid,
    admin_decision character varying(50),
    admin_notes text,
    CONSTRAINT content_moderation_queue_priority_check CHECK (((priority)::text = ANY ((ARRAY['low'::character varying, 'normal'::character varying, 'high'::character varying, 'urgent'::character varying])::text[]))),
    CONSTRAINT content_moderation_queue_status_check CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'approved'::character varying, 'rejected'::character varying, 'escalated'::character varying])::text[])))
);


--
-- Name: TABLE content_moderation_queue; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.content_moderation_queue IS 'Queue for content requiring manual review by administrators';


--
-- Name: csrf_tokens; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.csrf_tokens (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id character varying(255) NOT NULL,
    token character varying(255) NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE csrf_tokens; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.csrf_tokens IS 'CSRF protection tokens for secure form submissions';


--
-- Name: daily_analytics_summary; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.daily_analytics_summary (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    date date NOT NULL,
    total_company_views integer DEFAULT 0,
    total_searches integer DEFAULT 0,
    total_benefit_interactions integer DEFAULT 0,
    unique_visitors integer DEFAULT 0,
    unique_searchers integer DEFAULT 0,
    top_searched_benefits jsonb,
    top_viewed_companies jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE daily_analytics_summary; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.daily_analytics_summary IS 'Platform-wide daily analytics aggregations for admin dashboard';


--
-- Name: data_deletion_requests; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.data_deletion_requests (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    email character varying(255) NOT NULL,
    reason text,
    status character varying(50) DEFAULT 'pending'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    processed_at timestamp with time zone,
    processed_by uuid,
    notes text,
    CONSTRAINT data_deletion_requests_status_check CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'processing'::character varying, 'completed'::character varying, 'cancelled'::character varying])::text[])))
);


--
-- Name: TABLE data_deletion_requests; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.data_deletion_requests IS 'Tracks user requests for data deletion under GDPR Article 17 (Right to Erasure)';


--
-- Name: data_export_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.data_export_logs (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    email character varying(255) NOT NULL,
    export_type character varying(50) DEFAULT 'full'::character varying NOT NULL,
    ip_address inet,
    user_agent text,
    created_at timestamp with time zone DEFAULT now(),
    file_size_bytes bigint,
    data_types jsonb
);


--
-- Name: TABLE data_export_logs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.data_export_logs IS 'Logs user data export requests under GDPR Article 15 (Right of Access)';


--
-- Name: magic_link_rate_limits; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.magic_link_rate_limits (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    email character varying(255) NOT NULL,
    request_count integer DEFAULT 1,
    window_start timestamp with time zone DEFAULT now(),
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE magic_link_rate_limits; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.magic_link_rate_limits IS 'Rate limiting for magic link requests to prevent abuse';


--
-- Name: magic_link_tokens; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.magic_link_tokens (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    token character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    user_data jsonb,
    expires_at timestamp with time zone NOT NULL,
    used_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE magic_link_tokens; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.magic_link_tokens IS 'Temporary tokens for passwordless authentication via magic links';


--
-- Name: migration_log; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.migration_log (
    id integer NOT NULL,
    migration_name character varying(255) NOT NULL,
    description text,
    applied_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE migration_log; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.migration_log IS 'Database migration tracking for deployment and rollback management';


--
-- Name: migration_log_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.migration_log_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: migration_log_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.migration_log_id_seq OWNED BY public.migration_log.id;


--
-- Name: missing_company_reports; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.missing_company_reports (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_email character varying(255) NOT NULL,
    email_domain character varying(255) NOT NULL,
    first_name character varying(255),
    last_name character varying(255),
    status character varying(50) DEFAULT 'pending'::character varying,
    admin_notes text,
    company_id uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT missing_company_reports_status_check CHECK (((status)::text = ANY (ARRAY[('pending'::character varying)::text, ('reviewed'::character varying)::text, ('added'::character varying)::text, ('rejected'::character varying)::text])))
);


--
-- Name: TABLE missing_company_reports; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.missing_company_reports IS 'User reports requesting addition of missing companies to the platform';


--
-- Name: moderation_actions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.moderation_actions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    queue_item_id uuid,
    action_type character varying(50) NOT NULL,
    performed_by uuid,
    reason text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT moderation_actions_action_type_check CHECK (((action_type)::text = ANY ((ARRAY['auto_approve'::character varying, 'auto_reject'::character varying, 'manual_approve'::character varying, 'manual_reject'::character varying, 'escalate'::character varying, 'flag'::character varying])::text[])))
);


--
-- Name: TABLE moderation_actions; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.moderation_actions IS 'Log of all moderation actions taken on content items';


--
-- Name: rate_limits; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.rate_limits (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    identifier character varying(255) NOT NULL,
    window_start timestamp with time zone NOT NULL,
    request_count integer DEFAULT 1,
    request_timestamps jsonb DEFAULT '[]'::jsonb,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE rate_limits; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.rate_limits IS 'General rate limiting system for API and feature access control';


--
-- Name: saved_companies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.saved_companies (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id character varying(255) NOT NULL,
    company_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE saved_companies; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.saved_companies IS 'Companies saved by users for quick access and comparison';


--
-- Name: search_queries; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.search_queries (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    query_text text NOT NULL,
    user_id uuid,
    session_id character varying(255),
    results_count integer DEFAULT 0,
    filters_applied jsonb,
    ip_address inet,
    user_agent text,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE search_queries; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.search_queries IS 'User search queries for analytics and search optimization';


--
-- Name: session_config; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.session_config (
    id integer NOT NULL,
    setting_name character varying(100) NOT NULL,
    setting_value text NOT NULL,
    description text,
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE session_config; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.session_config IS 'Configuration settings for session management and security';


--
-- Name: session_config_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.session_config_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: session_config_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.session_config_id_seq OWNED BY public.session_config.id;


--
-- Name: user_sessions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_sessions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    session_token character varying(255) NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE user_sessions; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.user_sessions IS 'Active user sessions for authentication and session management';


--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    email character varying(255) NOT NULL,
    first_name character varying(255),
    last_name character varying(255),
    email_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    role character varying(50) DEFAULT 'user'::character varying,
    payment_status character varying(50) DEFAULT 'free'::character varying,
    company_id uuid,
    deletion_requested_at timestamp with time zone,
    deletion_reason text,
    CONSTRAINT users_payment_status_check CHECK (((payment_status)::text = ANY (ARRAY[('free'::character varying)::text, ('paying'::character varying)::text]))),
    CONSTRAINT users_role_check CHECK (((role)::text = ANY (ARRAY[('user'::character varying)::text, ('admin'::character varying)::text])))
);


--
-- Name: TABLE users; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.users IS 'Registered users of the BenefitLens platform with company associations and payment status';


--
-- Name: COLUMN users.company_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.users.company_id IS 'Explicit company association for user - takes precedence over email domain matching';


--
-- Name: COLUMN users.deletion_requested_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.users.deletion_requested_at IS 'Timestamp when user requested account deletion';


--
-- Name: COLUMN users.deletion_reason; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.users.deletion_reason IS 'User-provided reason for account deletion request';


--
-- Name: session_monitoring; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.session_monitoring AS
 SELECT u.email,
    u.first_name,
    u.last_name,
    us.session_token,
    us.created_at,
    us.expires_at,
        CASE
            WHEN (us.expires_at < now()) THEN 'expired'::text
            ELSE 'active'::text
        END AS status,
    (EXTRACT(epoch FROM (us.expires_at - now())) / (3600)::numeric) AS hours_until_expiry
   FROM (public.user_sessions us
     JOIN public.users u ON ((us.user_id = u.id)))
  ORDER BY us.created_at DESC;


--
-- Name: user_benefit_rankings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_benefit_rankings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    benefit_id uuid NOT NULL,
    ranking integer NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT user_benefit_rankings_ranking_check CHECK (((ranking >= 1) AND (ranking <= 10)))
);


--
-- Name: TABLE user_benefit_rankings; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.user_benefit_rankings IS 'User-defined benefit priority rankings for personalized recommendations';


--
-- Name: migration_log id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migration_log ALTER COLUMN id SET DEFAULT nextval('public.migration_log_id_seq'::regclass);


--
-- Name: session_config id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.session_config ALTER COLUMN id SET DEFAULT nextval('public.session_config_id_seq'::regclass);


--
-- Name: activity_log activity_log_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.activity_log
    ADD CONSTRAINT activity_log_pkey PRIMARY KEY (id);


--
-- Name: auth_logs auth_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.auth_logs
    ADD CONSTRAINT auth_logs_pkey PRIMARY KEY (id);


--
-- Name: benefit_categories benefit_categories_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefit_categories
    ADD CONSTRAINT benefit_categories_name_key UNIQUE (name);


--
-- Name: benefit_categories benefit_categories_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefit_categories
    ADD CONSTRAINT benefit_categories_pkey PRIMARY KEY (id);


--
-- Name: benefit_removal_disputes benefit_removal_disputes_company_benefit_id_user_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefit_removal_disputes
    ADD CONSTRAINT benefit_removal_disputes_company_benefit_id_user_id_key UNIQUE (company_benefit_id, user_id);


--
-- Name: benefit_removal_disputes benefit_removal_disputes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefit_removal_disputes
    ADD CONSTRAINT benefit_removal_disputes_pkey PRIMARY KEY (id);


--
-- Name: benefit_search_interactions benefit_search_interactions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_pkey PRIMARY KEY (id);


--
-- Name: benefit_verifications benefit_verifications_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefit_verifications
    ADD CONSTRAINT benefit_verifications_pkey PRIMARY KEY (id);


--
-- Name: benefits benefits_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefits
    ADD CONSTRAINT benefits_name_key UNIQUE (name);


--
-- Name: benefits benefits_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefits
    ADD CONSTRAINT benefits_pkey PRIMARY KEY (id);


--
-- Name: cache_store cache_store_cache_key_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cache_store
    ADD CONSTRAINT cache_store_cache_key_key UNIQUE (cache_key);


--
-- Name: cache_store cache_store_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cache_store
    ADD CONSTRAINT cache_store_pkey PRIMARY KEY (id);


--
-- Name: companies companies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT companies_pkey PRIMARY KEY (id);


--
-- Name: company_analytics_summary company_analytics_summary_company_id_date_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_analytics_summary
    ADD CONSTRAINT company_analytics_summary_company_id_date_key UNIQUE (company_id, date);


--
-- Name: company_analytics_summary company_analytics_summary_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_analytics_summary
    ADD CONSTRAINT company_analytics_summary_pkey PRIMARY KEY (id);


--
-- Name: company_benefits company_benefits_company_id_benefit_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_benefits
    ADD CONSTRAINT company_benefits_company_id_benefit_id_key UNIQUE (company_id, benefit_id);


--
-- Name: company_benefits company_benefits_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_benefits
    ADD CONSTRAINT company_benefits_pkey PRIMARY KEY (id);


--
-- Name: company_locations company_locations_company_id_location_normalized_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_locations
    ADD CONSTRAINT company_locations_company_id_location_normalized_key UNIQUE (company_id, location_normalized);


--
-- Name: company_locations company_locations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_locations
    ADD CONSTRAINT company_locations_pkey PRIMARY KEY (id);


--
-- Name: company_page_views company_page_views_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_page_views
    ADD CONSTRAINT company_page_views_pkey PRIMARY KEY (id);


--
-- Name: company_verification_tokens company_verification_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_verification_tokens
    ADD CONSTRAINT company_verification_tokens_pkey PRIMARY KEY (id);


--
-- Name: company_verification_tokens company_verification_tokens_token_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_verification_tokens
    ADD CONSTRAINT company_verification_tokens_token_key UNIQUE (token);


--
-- Name: content_moderation_queue content_moderation_queue_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.content_moderation_queue
    ADD CONSTRAINT content_moderation_queue_pkey PRIMARY KEY (id);


--
-- Name: csrf_tokens csrf_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.csrf_tokens
    ADD CONSTRAINT csrf_tokens_pkey PRIMARY KEY (id);


--
-- Name: csrf_tokens csrf_tokens_session_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.csrf_tokens
    ADD CONSTRAINT csrf_tokens_session_id_key UNIQUE (session_id);


--
-- Name: daily_analytics_summary daily_analytics_summary_date_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.daily_analytics_summary
    ADD CONSTRAINT daily_analytics_summary_date_key UNIQUE (date);


--
-- Name: daily_analytics_summary daily_analytics_summary_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.daily_analytics_summary
    ADD CONSTRAINT daily_analytics_summary_pkey PRIMARY KEY (id);


--
-- Name: data_deletion_requests data_deletion_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_deletion_requests
    ADD CONSTRAINT data_deletion_requests_pkey PRIMARY KEY (id);


--
-- Name: data_export_logs data_export_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_export_logs
    ADD CONSTRAINT data_export_logs_pkey PRIMARY KEY (id);


--
-- Name: magic_link_rate_limits magic_link_rate_limits_email_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.magic_link_rate_limits
    ADD CONSTRAINT magic_link_rate_limits_email_key UNIQUE (email);


--
-- Name: magic_link_rate_limits magic_link_rate_limits_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.magic_link_rate_limits
    ADD CONSTRAINT magic_link_rate_limits_pkey PRIMARY KEY (id);


--
-- Name: magic_link_tokens magic_link_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.magic_link_tokens
    ADD CONSTRAINT magic_link_tokens_pkey PRIMARY KEY (id);


--
-- Name: magic_link_tokens magic_link_tokens_token_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.magic_link_tokens
    ADD CONSTRAINT magic_link_tokens_token_key UNIQUE (token);


--
-- Name: migration_log migration_log_migration_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migration_log
    ADD CONSTRAINT migration_log_migration_name_key UNIQUE (migration_name);


--
-- Name: migration_log migration_log_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migration_log
    ADD CONSTRAINT migration_log_pkey PRIMARY KEY (id);


--
-- Name: missing_company_reports missing_company_reports_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.missing_company_reports
    ADD CONSTRAINT missing_company_reports_pkey PRIMARY KEY (id);


--
-- Name: moderation_actions moderation_actions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.moderation_actions
    ADD CONSTRAINT moderation_actions_pkey PRIMARY KEY (id);


--
-- Name: rate_limits rate_limits_identifier_window_start_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.rate_limits
    ADD CONSTRAINT rate_limits_identifier_window_start_key UNIQUE (identifier, window_start);


--
-- Name: rate_limits rate_limits_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.rate_limits
    ADD CONSTRAINT rate_limits_pkey PRIMARY KEY (id);


--
-- Name: saved_companies saved_companies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.saved_companies
    ADD CONSTRAINT saved_companies_pkey PRIMARY KEY (id);


--
-- Name: saved_companies saved_companies_user_id_company_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.saved_companies
    ADD CONSTRAINT saved_companies_user_id_company_id_key UNIQUE (user_id, company_id);


--
-- Name: search_queries search_queries_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.search_queries
    ADD CONSTRAINT search_queries_pkey PRIMARY KEY (id);


--
-- Name: session_config session_config_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.session_config
    ADD CONSTRAINT session_config_pkey PRIMARY KEY (id);


--
-- Name: session_config session_config_setting_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.session_config
    ADD CONSTRAINT session_config_setting_name_key UNIQUE (setting_name);


--
-- Name: user_benefit_rankings user_benefit_rankings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_benefit_rankings
    ADD CONSTRAINT user_benefit_rankings_pkey PRIMARY KEY (id);


--
-- Name: user_benefit_rankings user_benefit_rankings_user_id_benefit_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_benefit_rankings
    ADD CONSTRAINT user_benefit_rankings_user_id_benefit_id_key UNIQUE (user_id, benefit_id);


--
-- Name: user_sessions user_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_pkey PRIMARY KEY (id);


--
-- Name: user_sessions user_sessions_session_token_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_session_token_key UNIQUE (session_token);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: idx_activity_log_benefit_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_activity_log_benefit_id ON public.activity_log USING btree (benefit_id);


--
-- Name: idx_activity_log_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_activity_log_company_id ON public.activity_log USING btree (company_id);


--
-- Name: idx_activity_log_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_activity_log_created_at ON public.activity_log USING btree (created_at DESC);


--
-- Name: idx_activity_log_event_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_activity_log_event_type ON public.activity_log USING btree (event_type);


--
-- Name: idx_activity_log_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_activity_log_user_id ON public.activity_log USING btree (user_id);


--
-- Name: idx_auth_logs_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_auth_logs_created_at ON public.auth_logs USING btree (created_at);


--
-- Name: idx_auth_logs_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_auth_logs_email ON public.auth_logs USING btree (email);


--
-- Name: idx_auth_logs_event_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_auth_logs_event_type ON public.auth_logs USING btree (event_type);


--
-- Name: idx_auth_logs_failure_stats; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_auth_logs_failure_stats ON public.auth_logs USING btree (status, event_type, created_at) WHERE ((status)::text = 'failure'::text);


--
-- Name: idx_auth_logs_ip_address; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_auth_logs_ip_address ON public.auth_logs USING btree (ip_address);


--
-- Name: idx_auth_logs_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_auth_logs_status ON public.auth_logs USING btree (status);


--
-- Name: idx_auth_logs_status_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_auth_logs_status_created_at ON public.auth_logs USING btree (status, created_at);


--
-- Name: idx_benefit_categories_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_categories_name ON public.benefit_categories USING btree (name);


--
-- Name: idx_benefit_categories_sort_order; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_categories_sort_order ON public.benefit_categories USING btree (sort_order);


--
-- Name: idx_benefit_removal_disputes_company_benefit_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_removal_disputes_company_benefit_id ON public.benefit_removal_disputes USING btree (company_benefit_id);


--
-- Name: idx_benefit_removal_disputes_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_removal_disputes_created_at ON public.benefit_removal_disputes USING btree (created_at);


--
-- Name: idx_benefit_removal_disputes_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_removal_disputes_status ON public.benefit_removal_disputes USING btree (status);


--
-- Name: idx_benefit_removal_disputes_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_removal_disputes_user_id ON public.benefit_removal_disputes USING btree (user_id);


--
-- Name: idx_benefit_search_interactions_benefit_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_search_interactions_benefit_id ON public.benefit_search_interactions USING btree (benefit_id);


--
-- Name: idx_benefit_search_interactions_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_search_interactions_company_id ON public.benefit_search_interactions USING btree (company_id);


--
-- Name: idx_benefit_search_interactions_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_search_interactions_created_at ON public.benefit_search_interactions USING btree (created_at);


--
-- Name: idx_benefit_search_interactions_search_query_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_search_interactions_search_query_id ON public.benefit_search_interactions USING btree (search_query_id);


--
-- Name: idx_benefit_search_interactions_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_search_interactions_user_id ON public.benefit_search_interactions USING btree (user_id);


--
-- Name: idx_benefit_verifications_company_benefit_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_verifications_company_benefit_id ON public.benefit_verifications USING btree (company_benefit_id);


--
-- Name: idx_benefit_verifications_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_verifications_status ON public.benefit_verifications USING btree (status);


--
-- Name: idx_benefit_verifications_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefit_verifications_user_id ON public.benefit_verifications USING btree (user_id);


--
-- Name: idx_benefits_category_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefits_category_id ON public.benefits USING btree (category_id);


--
-- Name: idx_benefits_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefits_name ON public.benefits USING btree (name);


--
-- Name: idx_benefits_with_categories_cache_category; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefits_with_categories_cache_category ON public.benefits_with_categories_cache USING btree (category_id);


--
-- Name: idx_benefits_with_categories_cache_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_benefits_with_categories_cache_id ON public.benefits_with_categories_cache USING btree (id);


--
-- Name: idx_benefits_with_categories_cache_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_benefits_with_categories_cache_name ON public.benefits_with_categories_cache USING gin (to_tsvector('english'::regconfig, (name)::text));


--
-- Name: idx_cache_store_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_cache_store_created_at ON public.cache_store USING btree (created_at);


--
-- Name: idx_cache_store_expires_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_cache_store_expires_at ON public.cache_store USING btree (expires_at);


--
-- Name: idx_cache_store_key; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_cache_store_key ON public.cache_store USING btree (cache_key);


--
-- Name: idx_companies_domain; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_companies_domain ON public.companies USING btree (domain);


--
-- Name: idx_companies_founded_year; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_companies_founded_year ON public.companies USING btree (founded_year) WHERE (founded_year IS NOT NULL);


--
-- Name: idx_companies_industry; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_companies_industry ON public.companies USING btree (industry);


--
-- Name: idx_companies_size; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_companies_size ON public.companies USING btree (size);


--
-- Name: idx_companies_website; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_companies_website ON public.companies USING btree (website) WHERE (website IS NOT NULL);


--
-- Name: idx_companies_with_benefits_cache_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_companies_with_benefits_cache_id ON public.companies_with_benefits_cache USING btree (id);


--
-- Name: idx_companies_with_benefits_cache_industry; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_companies_with_benefits_cache_industry ON public.companies_with_benefits_cache USING btree (industry);


--
-- Name: idx_companies_with_benefits_cache_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_companies_with_benefits_cache_name ON public.companies_with_benefits_cache USING gin (to_tsvector('english'::regconfig, (name)::text));


--
-- Name: idx_companies_with_benefits_cache_size; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_companies_with_benefits_cache_size ON public.companies_with_benefits_cache USING btree (size);


--
-- Name: idx_company_analytics_summary_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_analytics_summary_company_id ON public.company_analytics_summary USING btree (company_id);


--
-- Name: idx_company_analytics_summary_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_analytics_summary_date ON public.company_analytics_summary USING btree (date);


--
-- Name: idx_company_benefits_benefit_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_benefits_benefit_id ON public.company_benefits USING btree (benefit_id);


--
-- Name: idx_company_benefits_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_benefits_company_id ON public.company_benefits USING btree (company_id);


--
-- Name: idx_company_benefits_verified; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_benefits_verified ON public.company_benefits USING btree (is_verified);


--
-- Name: idx_company_locations_city; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_locations_city ON public.company_locations USING btree (city);


--
-- Name: idx_company_locations_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_locations_company_id ON public.company_locations USING btree (company_id);


--
-- Name: idx_company_locations_coords; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_locations_coords ON public.company_locations USING btree (latitude, longitude) WHERE ((latitude IS NOT NULL) AND (longitude IS NOT NULL));


--
-- Name: idx_company_locations_country; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_locations_country ON public.company_locations USING btree (country);


--
-- Name: idx_company_locations_country_code; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_locations_country_code ON public.company_locations USING btree (country_code);


--
-- Name: idx_company_locations_headquarters; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_locations_headquarters ON public.company_locations USING btree (is_headquarters) WHERE (is_headquarters = true);


--
-- Name: idx_company_locations_normalized; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_locations_normalized ON public.company_locations USING btree (location_normalized);


--
-- Name: idx_company_locations_primary; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_locations_primary ON public.company_locations USING btree (is_primary) WHERE (is_primary = true);


--
-- Name: idx_company_locations_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_locations_type ON public.company_locations USING btree (location_type);


--
-- Name: idx_company_page_views_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_page_views_company_id ON public.company_page_views USING btree (company_id);


--
-- Name: idx_company_page_views_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_page_views_created_at ON public.company_page_views USING btree (created_at);


--
-- Name: idx_company_page_views_session_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_page_views_session_id ON public.company_page_views USING btree (session_id);


--
-- Name: idx_company_page_views_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_page_views_user_id ON public.company_page_views USING btree (user_id);


--
-- Name: idx_company_verification_tokens_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_verification_tokens_company_id ON public.company_verification_tokens USING btree (company_id);


--
-- Name: idx_company_verification_tokens_expires_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_verification_tokens_expires_at ON public.company_verification_tokens USING btree (expires_at);


--
-- Name: idx_company_verification_tokens_token; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_verification_tokens_token ON public.company_verification_tokens USING btree (token);


--
-- Name: idx_company_verification_tokens_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_company_verification_tokens_user_id ON public.company_verification_tokens USING btree (user_id);


--
-- Name: idx_content_moderation_queue_content_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_content_moderation_queue_content_type ON public.content_moderation_queue USING btree (content_type);


--
-- Name: idx_content_moderation_queue_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_content_moderation_queue_created_at ON public.content_moderation_queue USING btree (created_at);


--
-- Name: idx_content_moderation_queue_priority; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_content_moderation_queue_priority ON public.content_moderation_queue USING btree (priority);


--
-- Name: idx_content_moderation_queue_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_content_moderation_queue_status ON public.content_moderation_queue USING btree (status);


--
-- Name: idx_content_moderation_queue_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_content_moderation_queue_user_id ON public.content_moderation_queue USING btree (user_id);


--
-- Name: idx_csrf_tokens_expires_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_csrf_tokens_expires_at ON public.csrf_tokens USING btree (expires_at);


--
-- Name: idx_csrf_tokens_session_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_csrf_tokens_session_id ON public.csrf_tokens USING btree (session_id);


--
-- Name: idx_daily_analytics_summary_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_daily_analytics_summary_date ON public.daily_analytics_summary USING btree (date);


--
-- Name: idx_data_deletion_requests_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_data_deletion_requests_created_at ON public.data_deletion_requests USING btree (created_at);


--
-- Name: idx_data_deletion_requests_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_data_deletion_requests_status ON public.data_deletion_requests USING btree (status);


--
-- Name: idx_data_deletion_requests_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_data_deletion_requests_user_id ON public.data_deletion_requests USING btree (user_id);


--
-- Name: idx_data_export_logs_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_data_export_logs_created_at ON public.data_export_logs USING btree (created_at);


--
-- Name: idx_data_export_logs_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_data_export_logs_user_id ON public.data_export_logs USING btree (user_id);


--
-- Name: idx_magic_link_rate_limits_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_magic_link_rate_limits_email ON public.magic_link_rate_limits USING btree (email);


--
-- Name: idx_magic_link_rate_limits_window_start; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_magic_link_rate_limits_window_start ON public.magic_link_rate_limits USING btree (window_start);


--
-- Name: idx_magic_link_tokens_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_magic_link_tokens_email ON public.magic_link_tokens USING btree (email);


--
-- Name: idx_magic_link_tokens_expires_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_magic_link_tokens_expires_at ON public.magic_link_tokens USING btree (expires_at);


--
-- Name: idx_magic_link_tokens_token; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_magic_link_tokens_token ON public.magic_link_tokens USING btree (token);


--
-- Name: idx_missing_company_reports_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_missing_company_reports_created_at ON public.missing_company_reports USING btree (created_at);


--
-- Name: idx_missing_company_reports_email_domain; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_missing_company_reports_email_domain ON public.missing_company_reports USING btree (email_domain);


--
-- Name: idx_missing_company_reports_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_missing_company_reports_status ON public.missing_company_reports USING btree (status);


--
-- Name: idx_missing_company_reports_user_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_missing_company_reports_user_email ON public.missing_company_reports USING btree (user_email);


--
-- Name: idx_moderation_actions_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_moderation_actions_created_at ON public.moderation_actions USING btree (created_at);


--
-- Name: idx_moderation_actions_performed_by; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_moderation_actions_performed_by ON public.moderation_actions USING btree (performed_by);


--
-- Name: idx_moderation_actions_queue_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_moderation_actions_queue_item_id ON public.moderation_actions USING btree (queue_item_id);


--
-- Name: idx_rate_limits_expires_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_rate_limits_expires_at ON public.rate_limits USING btree (expires_at);


--
-- Name: idx_rate_limits_identifier; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_rate_limits_identifier ON public.rate_limits USING btree (identifier);


--
-- Name: idx_rate_limits_window_start; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_rate_limits_window_start ON public.rate_limits USING btree (window_start);


--
-- Name: idx_search_queries_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_search_queries_created_at ON public.search_queries USING btree (created_at);


--
-- Name: idx_search_queries_query_text; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_search_queries_query_text ON public.search_queries USING gin (to_tsvector('english'::regconfig, query_text));


--
-- Name: idx_search_queries_session_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_search_queries_session_id ON public.search_queries USING btree (session_id);


--
-- Name: idx_search_queries_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_search_queries_user_id ON public.search_queries USING btree (user_id);


--
-- Name: idx_user_benefit_rankings_benefit_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_benefit_rankings_benefit_id ON public.user_benefit_rankings USING btree (benefit_id);


--
-- Name: idx_user_benefit_rankings_ranking; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_benefit_rankings_ranking ON public.user_benefit_rankings USING btree (ranking);


--
-- Name: idx_user_benefit_rankings_updated_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_benefit_rankings_updated_at ON public.user_benefit_rankings USING btree (updated_at);


--
-- Name: idx_user_benefit_rankings_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_benefit_rankings_user_id ON public.user_benefit_rankings USING btree (user_id);


--
-- Name: idx_user_sessions_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_sessions_created_at ON public.user_sessions USING btree (created_at);


--
-- Name: idx_user_sessions_expires_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_sessions_expires_at ON public.user_sessions USING btree (expires_at);


--
-- Name: idx_user_sessions_expires_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_sessions_expires_created ON public.user_sessions USING btree (expires_at, created_at);


--
-- Name: idx_user_sessions_token; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_sessions_token ON public.user_sessions USING btree (session_token);


--
-- Name: idx_user_sessions_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_sessions_user_id ON public.user_sessions USING btree (user_id);


--
-- Name: idx_users_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_company_id ON public.users USING btree (company_id);


--
-- Name: idx_users_deletion_requested_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_deletion_requested_at ON public.users USING btree (deletion_requested_at);


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- Name: idx_users_payment_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_payment_status ON public.users USING btree (payment_status);


--
-- Name: benefit_removal_disputes update_benefit_removal_disputes_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_benefit_removal_disputes_updated_at BEFORE UPDATE ON public.benefit_removal_disputes FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: companies update_companies_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON public.companies FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: company_analytics_summary update_company_analytics_summary_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_company_analytics_summary_updated_at BEFORE UPDATE ON public.company_analytics_summary FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: daily_analytics_summary update_daily_analytics_summary_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_daily_analytics_summary_updated_at BEFORE UPDATE ON public.daily_analytics_summary FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: missing_company_reports update_missing_company_reports_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_missing_company_reports_updated_at BEFORE UPDATE ON public.missing_company_reports FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: user_benefit_rankings update_user_benefit_rankings_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_user_benefit_rankings_updated_at BEFORE UPDATE ON public.user_benefit_rankings FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: activity_log activity_log_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.activity_log
    ADD CONSTRAINT activity_log_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES public.benefits(id) ON DELETE SET NULL;


--
-- Name: activity_log activity_log_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.activity_log
    ADD CONSTRAINT activity_log_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE SET NULL;


--
-- Name: benefit_removal_disputes benefit_removal_disputes_company_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefit_removal_disputes
    ADD CONSTRAINT benefit_removal_disputes_company_benefit_id_fkey FOREIGN KEY (company_benefit_id) REFERENCES public.company_benefits(id) ON DELETE CASCADE;


--
-- Name: benefit_search_interactions benefit_search_interactions_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES public.benefits(id) ON DELETE CASCADE;


--
-- Name: benefit_search_interactions benefit_search_interactions_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: benefit_search_interactions benefit_search_interactions_search_query_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_search_query_id_fkey FOREIGN KEY (search_query_id) REFERENCES public.search_queries(id) ON DELETE CASCADE;


--
-- Name: benefit_search_interactions benefit_search_interactions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: benefit_verifications benefit_verifications_company_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefit_verifications
    ADD CONSTRAINT benefit_verifications_company_benefit_id_fkey FOREIGN KEY (company_benefit_id) REFERENCES public.company_benefits(id) ON DELETE CASCADE;


--
-- Name: benefits benefits_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.benefits
    ADD CONSTRAINT benefits_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.benefit_categories(id);


--
-- Name: company_analytics_summary company_analytics_summary_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_analytics_summary
    ADD CONSTRAINT company_analytics_summary_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_benefits company_benefits_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_benefits
    ADD CONSTRAINT company_benefits_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES public.benefits(id) ON DELETE CASCADE;


--
-- Name: company_benefits company_benefits_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_benefits
    ADD CONSTRAINT company_benefits_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_locations company_locations_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_locations
    ADD CONSTRAINT company_locations_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_page_views company_page_views_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_page_views
    ADD CONSTRAINT company_page_views_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_page_views company_page_views_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_page_views
    ADD CONSTRAINT company_page_views_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: company_verification_tokens company_verification_tokens_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_verification_tokens
    ADD CONSTRAINT company_verification_tokens_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_verification_tokens company_verification_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_verification_tokens
    ADD CONSTRAINT company_verification_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: content_moderation_queue content_moderation_queue_reviewed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.content_moderation_queue
    ADD CONSTRAINT content_moderation_queue_reviewed_by_fkey FOREIGN KEY (reviewed_by) REFERENCES public.users(id);


--
-- Name: content_moderation_queue content_moderation_queue_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.content_moderation_queue
    ADD CONSTRAINT content_moderation_queue_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: data_deletion_requests data_deletion_requests_processed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_deletion_requests
    ADD CONSTRAINT data_deletion_requests_processed_by_fkey FOREIGN KEY (processed_by) REFERENCES public.users(id);


--
-- Name: data_deletion_requests data_deletion_requests_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_deletion_requests
    ADD CONSTRAINT data_deletion_requests_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: data_export_logs data_export_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_export_logs
    ADD CONSTRAINT data_export_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: missing_company_reports missing_company_reports_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.missing_company_reports
    ADD CONSTRAINT missing_company_reports_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE SET NULL;


--
-- Name: moderation_actions moderation_actions_performed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.moderation_actions
    ADD CONSTRAINT moderation_actions_performed_by_fkey FOREIGN KEY (performed_by) REFERENCES public.users(id);


--
-- Name: moderation_actions moderation_actions_queue_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.moderation_actions
    ADD CONSTRAINT moderation_actions_queue_item_id_fkey FOREIGN KEY (queue_item_id) REFERENCES public.content_moderation_queue(id) ON DELETE CASCADE;


--
-- Name: saved_companies saved_companies_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.saved_companies
    ADD CONSTRAINT saved_companies_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: search_queries search_queries_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.search_queries
    ADD CONSTRAINT search_queries_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: user_benefit_rankings user_benefit_rankings_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_benefit_rankings
    ADD CONSTRAINT user_benefit_rankings_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES public.benefits(id) ON DELETE CASCADE;


--
-- Name: user_benefit_rankings user_benefit_rankings_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_benefit_rankings
    ADD CONSTRAINT user_benefit_rankings_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_sessions user_sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: users users_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE SET NULL;


--
-- Name: daily_analytics_summary Admin can view all analytics summaries; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Admin can view all analytics summaries" ON public.daily_analytics_summary FOR SELECT USING (true);


--
-- Name: benefit_search_interactions Admin can view all benefit interactions; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Admin can view all benefit interactions" ON public.benefit_search_interactions FOR SELECT USING (true);


--
-- Name: company_analytics_summary Admin can view all company analytics; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Admin can view all company analytics" ON public.company_analytics_summary FOR SELECT USING (true);


--
-- Name: company_page_views Admin can view all company page views; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Admin can view all company page views" ON public.company_page_views FOR SELECT USING (true);


--
-- Name: search_queries Admin can view all search queries; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Admin can view all search queries" ON public.search_queries FOR SELECT USING (true);


--
-- Name: benefit_removal_disputes Benefit removal disputes are viewable by admins; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Benefit removal disputes are viewable by admins" ON public.benefit_removal_disputes FOR SELECT USING (true);


--
-- Name: company_locations Public company locations are viewable by everyone; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Public company locations are viewable by everyone" ON public.company_locations FOR SELECT USING (true);


--
-- Name: benefit_removal_disputes; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.benefit_removal_disputes ENABLE ROW LEVEL SECURITY;

--
-- Name: benefit_search_interactions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.benefit_search_interactions ENABLE ROW LEVEL SECURITY;

--
-- Name: company_analytics_summary; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.company_analytics_summary ENABLE ROW LEVEL SECURITY;

--
-- Name: company_locations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.company_locations ENABLE ROW LEVEL SECURITY;

--
-- Name: company_page_views; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.company_page_views ENABLE ROW LEVEL SECURITY;

--
-- Name: daily_analytics_summary; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.daily_analytics_summary ENABLE ROW LEVEL SECURITY;

--
-- Name: search_queries; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.search_queries ENABLE ROW LEVEL SECURITY;

--
-- PostgreSQL database dump complete
--

